/* Cube AI Solutions Careers Page Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Header Styles */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.logo {
    font-size: 20px;
    font-weight: bold;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #3498db;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 50%, #9b59b6 100%);
    color: white;
    padding: 80px 20px;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

/* CTA Button */
.cta-button {
    background: #3498db;
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.cta-button:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

/* Opportunities Section */
.opportunities-section {
    padding: 80px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: #2c3e50;
}

.job-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

/* Job Cards */
.job-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.job-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.job-title {
    font-size: 1.4rem;
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
}

.job-department {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.job-description {
    color: #555;
    margin-bottom: 20px;
    line-height: 1.7;
}

.job-location {
    color: #3498db;
    font-weight: 500;
    font-size: 0.9rem;
}

/* Register Section */
.register-section {
    background: #2c3e50;
    color: white;
    padding: 60px 20px;
    text-align: center;
}

.register-content {
    max-width: 600px;
    margin: 0 auto;
}

.register-title {
    font-size: 2rem;
    margin-bottom: 15px;
}

.register-text {
    margin-bottom: 30px;
    opacity: 0.9;
    line-height: 1.7;
}

/* Footer */
.footer {
    background: #1a252f;
    color: white;
    text-align: center;
    padding: 40px 20px;
}

/* Loading Spinner */
.loading-spinner {
    display: none;
    text-align: center;
    padding: 20px;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Job Actions */
.job-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.btn.apply-btn {
    background: linear-gradient(90deg, #1e40af 0%, #3b82f6 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.2);
}

.btn.apply-btn:hover, .btn.apply-btn:focus {
    background: linear-gradient(90deg, #1e3a8a 0%, #2563eb 100%);
    box-shadow: 0 6px 25px rgba(30, 64, 175, 0.3);
    transform: translateY(-2px);
}

.btn.details-btn {
    background: #fff;
    color: #1e40af;
    border: 2px solid #1e40af;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn.details-btn:hover, .btn.details-btn:focus {
    background: #1e40af;
    color: #fff;
    border: 2px solid #1e40af;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .nav-menu {
        display: none;
    }
    
    .job-grid {
        grid-template-columns: 1fr;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .nav-container {
        padding: 0 15px;
    }
    
    .hero-section {
        padding: 60px 15px;
    }
    
    .opportunities-section {
        padding: 60px 15px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.8rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .job-card {
        padding: 20px;
    }
    
    .cta-button {
        padding: 12px 25px;
        font-size: 1rem;
    }
}