/* Cube AI Solutions - Vibrant Purple & Blue Theme */

/* Enhanced Color Palette */
:root {
    /* Primary Blue Shades */
    --primary-color: #1e40af;
    --primary-dark: #1e3a8a;
    --primary-light: #3b82f6;
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Secondary Sky Blue Shades */
    --secondary-color: #0ea5e9;
    --secondary-dark: #0284c7;
    --secondary-light: #38bdf8;
    --secondary-50: #f0f9ff;
    --secondary-100: #e0f2fe;
    --secondary-200: #bae6fd;
    --secondary-300: #7dd3fc;
    --secondary-400: #38bdf8;
    --secondary-500: #0ea5e9;
    --secondary-600: #0284c7;
    --secondary-700: #0369a1;
    --secondary-800: #075985;
    --secondary-900: #0c4a6e;

    /* Accent Colors */
    --accent-cyan: #06b6d4;
    --accent-purple: #d946ef;
    --accent-indigo: #6366f1;
    --accent-green: #10b981;
    --accent-orange: #f59e0b;
    --accent-red: #ef4444;

    /* Gradient Combinations */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
    --gradient-blue-sky: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --gradient-professional: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #0ea5e9 100%);
    --gradient-hero: linear-gradient(135deg, rgba(30, 64, 175, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);

    /* Shadow Colors */
    --shadow-primary: 0 4px 15px rgba(30, 64, 175, 0.3);
    --shadow-primary-hover: 0 6px 25px rgba(30, 64, 175, 0.4);
    --shadow-secondary: 0 4px 15px rgba(14, 165, 233, 0.3);
    --shadow-secondary-hover: 0 6px 25px rgba(14, 165, 233, 0.4);
    --shadow-professional: 0 8px 30px rgba(30, 64, 175, 0.2);

    /* Background Overlays */
    --overlay-primary: rgba(30, 64, 175, 0.05);
    --overlay-secondary: rgba(14, 165, 233, 0.05);
    --overlay-dark: rgba(30, 64, 175, 0.1);
}

/* Enhanced Button Styles */
.btn-vibrant {
    background: var(--gradient-vibrant);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-primary);
    position: relative;
    overflow: hidden;
}

.btn-vibrant::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-vibrant:hover::before {
    left: 100%;
}

.btn-vibrant:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-primary-hover);
}

/* Enhanced Card Styles */
.card-vibrant {
    background: white;
    border: 1px solid var(--primary-100);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.card-vibrant::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-vibrant);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card-vibrant:hover::before {
    opacity: 1;
}

.card-vibrant:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-vibrant);
    border-color: var(--primary-200);
}

/* Enhanced Navigation */
.nav-vibrant {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--primary-100);
}

.nav-link-vibrant {
    color: var(--text-secondary);
    transition: all 0.2s ease;
    position: relative;
}

.nav-link-vibrant::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link-vibrant:hover::after,
.nav-link-vibrant.active::after {
    width: 100%;
}

.nav-link-vibrant:hover,
.nav-link-vibrant.active {
    color: var(--primary-color);
}

/* Enhanced Form Elements */
.form-input-vibrant {
    border: 2px solid var(--primary-100);
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-input-vibrant:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--overlay-primary);
    background: var(--primary-50);
}

/* Enhanced Badges */
.badge-vibrant {
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
    box-shadow: var(--shadow-primary);
}

.badge-secondary {
    background: var(--gradient-secondary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
    box-shadow: var(--shadow-secondary);
}

/* Enhanced Text Highlights */
.text-vibrant {
    background: var(--gradient-purple-blue);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* Enhanced Animations */
@keyframes vibrantPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.4);
    }
    50% {
        box-shadow: 0 0 0 20px rgba(139, 92, 246, 0);
    }
}

.pulse-vibrant {
    animation: vibrantPulse 2s infinite;
}

@keyframes vibrantFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.float-vibrant {
    animation: vibrantFloat 3s ease-in-out infinite;
}

/* Enhanced Hover Effects */
.hover-vibrant {
    transition: all 0.3s ease;
}

.hover-vibrant:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

/* Enhanced Background Patterns */
.bg-pattern-vibrant {
    background-image: 
        radial-gradient(circle at 25% 25%, var(--overlay-primary) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, var(--overlay-secondary) 0%, transparent 50%);
    background-size: 400px 400px;
}

/* Enhanced Loading States */
.loading-vibrant {
    background: linear-gradient(90deg, var(--primary-100) 25%, var(--primary-200) 50%, var(--primary-100) 75%);
    background-size: 200% 100%;
    animation: loadingShimmer 1.5s infinite;
}

@keyframes loadingShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}
