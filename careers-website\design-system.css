/* Cube AI Solutions Design System */

/* CSS Custom Properties (Design Tokens) */
:root {
    /* Colors - Cube AI Solutions Professional Blue Palette */
    --primary-color: #1e40af;
    --primary-dark: #1e3a8a;
    --primary-light: #3b82f6;
    --secondary-color: #0ea5e9;
    --secondary-dark: #0284c7;
    --secondary-light: #38bdf8;

    /* Accent Colors */
    --accent-blue: #0ea5e9;
    --accent-navy: #1e3a8a;
    --accent-sky: #0284c7;
    --accent-green: #059669;
    --accent-orange: #ea580c;
    --accent-red: #dc2626;
    
    /* Neutral Colors */
    --text-primary: #212121;
    --text-secondary: #757575;
    --text-disabled: #bdbdbd;
    --background-primary: #ffffff;
    --background-secondary: #f5f5f5;
    --background-tertiary: #fafafa;
    --border-color: #e0e0e0;
    --border-light: #f0f0f0;
    
    /* Typography */
    --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    
    /* Font Sizes */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    --radius-2xl: 16px;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Enhanced Button Components */
.btn-cube-ai {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: 1.5;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-base);
    user-select: none;
    white-space: nowrap;
    min-height: 40px;
}

.btn-cube-ai:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn-cube-ai:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Button Variants */
.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
}

.btn-ghost {
    background-color: transparent;
    color: var(--text-secondary);
    border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
    background-color: var(--background-secondary);
    color: var(--text-primary);
}

/* Button Sizes */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
    min-height: 32px;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    min-height: 48px;
}

/* Card Components */
.card-cube-ai {
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    overflow: hidden;
}

.card-cube-ai:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    background: var(--background-tertiary);
}

/* Form Components */
.form-group-cube-ai {
    margin-bottom: var(--spacing-lg);
}

.form-label-cube-ai {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-input-cube-ai {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--background-primary);
    transition: all var(--transition-base);
}

.form-input-cube-ai:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.form-input-cube-ai:disabled {
    background-color: var(--background-secondary);
    color: var(--text-disabled);
    cursor: not-allowed;
}

/* Badge Components */
.badge-cube-ai {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    line-height: 1;
    border-radius: var(--radius-full);
    white-space: nowrap;
}

.badge-primary {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

.badge-secondary {
    background-color: var(--secondary-light);
    color: var(--secondary-dark);
}

.badge-success {
    background-color: #e8f5e8;
    color: var(--accent-green);
}

.badge-warning {
    background-color: #fff3e0;
    color: var(--accent-orange);
}

.badge-error {
    background-color: #ffebee;
    color: var(--accent-red);
}

/* Navigation Components */
.nav-cube-ai {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.nav-item-cube-ai {
    position: relative;
}

.nav-link-cube-ai {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    border-radius: var(--radius-md);
    transition: all var(--transition-base);
}

.nav-link-cube-ai:hover,
.nav-link-cube-ai.active {
    color: var(--primary-color);
    background-color: rgba(25, 118, 210, 0.08);
}

/* Utility Classes */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-disabled { color: var(--text-disabled); }

.bg-primary { background-color: var(--background-primary); }
.bg-secondary { background-color: var(--background-secondary); }
.bg-tertiary { background-color: var(--background-tertiary); }

.border { border: 1px solid var(--border-color); }
.border-light { border: 1px solid var(--border-light); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.transition-fast { transition: all var(--transition-fast); }
.transition-base { transition: all var(--transition-base); }
.transition-slow { transition: all var(--transition-slow); }

/* Responsive Utilities */
@media (max-width: 768px) {
    .btn-cube-ai {
        width: 100%;
        justify-content: center;
    }

    .nav-cube-ai {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .card-cube-ai {
        margin: var(--spacing-sm);
    }
}
