/* Enterprise-Grade Header Styles */

.enterprise-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 1px solid var(--border-light);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    gap: 2rem;
}

/* Logo Section */
.logo-section {
    flex-shrink: 0;
}

.enterprise-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.enterprise-logo:hover {
    transform: translateY(-1px);
    color: var(--primary-color);
}

.logo-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(25, 118, 210, 0.2);
    transition: all 0.3s ease;
}

.enterprise-logo:hover .logo-icon {
    box-shadow: 0 6px 25px rgba(25, 118, 210, 0.3);
    transform: scale(1.05);
}

.logo-text {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.company-name {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.2;
    color: var(--text-primary);
}

.company-tagline {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    opacity: 0.8;
}

/* Main Navigation */
.main-navigation {
    flex: 1;
    display: flex;
    justify-content: center;
}

.nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 12px;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    position: relative;
    white-space: nowrap;
}

.nav-link:hover {
    background: var(--background-secondary);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.nav-link.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
}

.nav-link.active:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(30, 64, 175, 0.4);
}

.nav-icon {
    font-size: 1rem;
    opacity: 0.8;
}

.job-count {
    background: var(--accent-orange);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    margin-left: 0.25rem;
    min-width: 1.5rem;
    text-align: center;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 280px;
    padding: 0.75rem 1rem;
    padding-right: 3rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 0.95rem;
    background: var(--background-primary);
    transition: all 0.2s ease;
    outline: none;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
    background: white;
}

.search-input::placeholder {
    color: var(--text-disabled);
    font-weight: 400;
}

.search-btn {
    position: absolute;
    right: 0.75rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.search-btn:hover {
    color: var(--primary-color);
    background: var(--background-secondary);
}

.action-buttons {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 0.25rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.mobile-menu-toggle:hover {
    background: var(--background-secondary);
}

.mobile-menu-toggle span {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    border-radius: 1px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.mobile-navigation {
    display: none;
    background: white;
    border-top: 1px solid var(--border-light);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.mobile-navigation.active {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mobile-nav-content {
    padding: 1.5rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.mobile-search {
    margin-bottom: 1.5rem;
}

.mobile-search-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 1rem;
    background: var(--background-primary);
    outline: none;
    transition: all 0.2s ease;
}

.mobile-search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.mobile-nav-links {
    list-style: none;
    margin: 0 0 1.5rem 0;
    padding: 0;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 1.1rem;
    border-bottom: 1px solid var(--border-light);
    transition: all 0.2s ease;
}

.mobile-nav-link:hover {
    color: var(--primary-color);
    padding-left: 0.5rem;
}

.mobile-nav-link:last-child {
    border-bottom: none;
}

.mobile-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.badge {
    background: var(--accent-orange);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    min-width: 1.5rem;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .search-input {
        width: 220px;
    }
    
    .nav-links {
        gap: 0.25rem;
    }
    
    .nav-link {
        padding: 0.625rem 1rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .header-container {
        padding: 0 1rem;
        height: 70px;
    }
    
    .main-navigation,
    .search-container {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .action-buttons {
        gap: 0.5rem;
    }
    
    .company-name {
        font-size: 1.25rem;
    }
    
    .company-tagline {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .header-container {
        padding: 0 0.75rem;
        gap: 1rem;
    }
    
    .logo-text {
        display: none;
    }
    
    .action-buttons .btn-secondary {
        display: none;
    }
}
