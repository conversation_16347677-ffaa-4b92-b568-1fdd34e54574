/* Enterprise Authentication System */

/* Auth Modal Overlay */
.auth-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.auth-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Auth Modal Container */
.auth-modal {
    background: white;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    max-width: 480px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
}

.auth-overlay.active .auth-modal {
    transform: scale(1) translateY(0);
}

/* Close Button */
.auth-close {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: var(--background-secondary);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 1;
}

.auth-close:hover {
    background: var(--border-color);
    transform: scale(1.1);
}

.auth-close::before {
    content: '✕';
    font-size: 1.2rem;
    color: var(--text-secondary);
}

/* Auth Header */
.auth-header {
    text-align: center;
    padding: 3rem 2rem 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 24px 24px 0 0;
    position: relative;
}

.auth-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(30, 64, 175, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
    border-radius: 24px 24px 0 0;
    pointer-events: none;
}

.auth-logo {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
    position: relative;
    z-index: 1;
}

.auth-logo svg {
    width: 32px;
    height: 32px;
    fill: white;
}

.auth-title {
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
    position: relative;
    z-index: 1;
}

.auth-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin: 0;
    position: relative;
    z-index: 1;
}

/* Tab Switcher */
.auth-tabs {
    display: flex;
    background: var(--background-secondary);
    border-radius: 12px;
    padding: 0.5rem;
    margin: 2rem 2rem 0;
    position: relative;
}

.auth-tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    z-index: 2;
}

.auth-tab.active {
    background: white;
    color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Auth Content */
.auth-content {
    padding: 2rem;
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
    animation: fadeInUp 0.3s ease;
}

/* Form Groups */
.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.form-input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.form-input.error {
    border-color: #f44336;
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

.form-input.success {
    border-color: #4caf50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* Password Input with Toggle */
.password-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.password-toggle:hover {
    background: var(--background-secondary);
    color: var(--primary-color);
}

/* Error Messages */
.form-error {
    color: #f44336;
    font-size: 0.85rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.form-error.show {
    opacity: 1;
    transform: translateY(0);
}

.form-error::before {
    content: '⚠️';
    font-size: 1rem;
}

/* Success Messages */
.form-success {
    color: #4caf50;
    font-size: 0.85rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.form-success.show {
    opacity: 1;
    transform: translateY(0);
}

.form-success::before {
    content: '✅';
    font-size: 1rem;
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 0.5rem;
    display: none;
}

.password-strength.show {
    display: block;
    animation: fadeInUp 0.3s ease;
}

.strength-bar {
    height: 4px;
    background: var(--border-light);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-fill.weak {
    width: 25%;
    background: #f44336;
}

.strength-fill.fair {
    width: 50%;
    background: #ff9800;
}

.strength-fill.good {
    width: 75%;
    background: #2196f3;
}

.strength-fill.strong {
    width: 100%;
    background: #4caf50;
}

.strength-text {
    font-size: 0.8rem;
    color: var(--text-secondary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Remember Me & Forgot Password */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1.5rem 0;
    gap: 1rem;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-input {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.checkbox-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    cursor: pointer;
}

.forgot-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.forgot-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Submit Button */
.auth-submit {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.auth-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(139, 92, 246, 0.4);
}

.auth-submit:active {
    transform: translateY(0);
}

.auth-submit.loading {
    pointer-events: none;
}

.auth-submit.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.auth-submit.loading .submit-text {
    opacity: 0;
}

/* Social Login */
.social-divider {
    display: flex;
    align-items: center;
    margin: 2rem 0;
    color: var(--text-disabled);
    font-size: 0.9rem;
}

.social-divider::before,
.social-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: var(--border-light);
}

.social-divider span {
    padding: 0 1rem;
    background: white;
}

.social-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.social-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    background: white;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
}

.social-btn:hover {
    border-color: var(--primary-light);
    background: rgba(139, 92, 246, 0.05);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.social-btn img {
    width: 20px;
    height: 20px;
}

/* Terms and Privacy */
.auth-terms {
    text-align: center;
    font-size: 0.85rem;
    color: var(--text-disabled);
    line-height: 1.5;
}

.auth-terms a {
    color: var(--primary-color);
    text-decoration: none;
}

.auth-terms a:hover {
    text-decoration: underline;
}

/* Success/Error Notifications */
.auth-notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: white;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border-left: 4px solid;
    z-index: 10001;
    transform: translateX(100%);
    transition: all 0.3s ease;
    max-width: 400px;
}

.auth-notification.show {
    transform: translateX(0);
}

.auth-notification.success {
    border-left-color: #4caf50;
}

.auth-notification.error {
    border-left-color: #f44336;
}

.auth-notification.info {
    border-left-color: #2196f3;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification-icon {
    font-size: 1.5rem;
}

.notification-text {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-modal {
        max-width: 95%;
        margin: 1rem;
        border-radius: 16px;
    }
    
    .auth-header {
        padding: 2rem 1.5rem 1rem;
        border-radius: 16px 16px 0 0;
    }
    
    .auth-content {
        padding: 1.5rem;
    }
    
    .auth-title {
        font-size: 1.75rem;
    }
    
    .social-buttons {
        flex-direction: column;
    }
    
    .form-options {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .auth-notification {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }
}

/* User Menu Styles */
.user-menu {
    position: relative;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: white;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.user-profile:hover {
    border-color: var(--primary-color);
    background: rgba(139, 92, 246, 0.05);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dropdown-arrow {
    color: var(--text-secondary);
    font-size: 0.8rem;
    transition: transform 0.2s ease;
}

.user-profile:hover .dropdown-arrow {
    transform: rotate(180deg);
}

.user-dropdown {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    background: white;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    z-index: 1000;
}

.user-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.95rem;
}

.dropdown-item:hover {
    background: var(--background-secondary);
    color: var(--primary-color);
}

.dropdown-item:first-child {
    border-radius: 10px 10px 0 0;
}

.dropdown-item:last-child {
    border-radius: 0 0 10px 10px;
}

.dropdown-divider {
    height: 1px;
    background: var(--border-light);
    margin: 0.5rem 0;
}

.logout-btn {
    color: #f44336 !important;
}

.logout-btn:hover {
    background: rgba(244, 67, 54, 0.1) !important;
    color: #f44336 !important;
}

/* Enhanced Auth Buttons */
.btn-outline.auth-trigger {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
}

.btn-outline.auth-trigger:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(139, 92, 246, 0.05);
    transform: translateY(-1px);
}

.btn-primary.auth-trigger {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.btn-primary.auth-trigger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(139, 92, 246, 0.4);
    color: white;
}
