/* Enterprise Opportunities Section */

.enterprise-opportunities {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%);
    position: relative;
}

.enterprise-opportunities::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(30, 64, 175, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
    background-size: 600px 600px;
    pointer-events: none;
}

.opportunities-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 1;
}

/* Section Header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 3rem;
    gap: 2rem;
}

.header-content {
    flex: 1;
}

.section-title {
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-primary);
    margin: 0 0 1rem 0;
    line-height: 1.1;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
    max-width: 600px;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-shrink: 0;
}

.filter-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-toggle:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(30, 64, 175, 0.05);
}

.view-all-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
}

.view-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(30, 64, 175, 0.4);
    color: white;
}

/* Quick Filters */
.quick-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-chip {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    border: 2px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.75rem 1.25rem;
    border-radius: 50px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.filter-chip:hover {
    border-color: var(--primary-light);
    color: var(--primary-color);
    background: rgba(30, 64, 175, 0.05);
}

.filter-chip.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
}

.filter-chip .count {
    background: rgba(255, 255, 255, 0.2);
    color: inherit;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    min-width: 1.5rem;
    text-align: center;
}

.filter-chip.active .count {
    background: rgba(255, 255, 255, 0.3);
}

/* Featured Jobs Grid */
.featured-jobs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Enterprise Job Card */
.enterprise-job-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.enterprise-job-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.enterprise-job-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.enterprise-job-card:hover::before {
    opacity: 1;
}

.enterprise-job-card.featured {
    border: 2px solid var(--primary-light);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
}

.enterprise-job-card.featured::before {
    opacity: 1;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
}

/* Job Header */
.job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.job-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    box-shadow: 0 2px 10px rgba(139, 92, 246, 0.3);
}

.job-badge.urgent {
    background: linear-gradient(135deg, #ff5722 0%, #ff7043 100%);
    box-shadow: 0 2px 10px rgba(255, 87, 34, 0.3);
}

.job-badge.remote {
    background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
    box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
}

.badge-icon {
    font-size: 1rem;
}

.job-type {
    background: var(--background-secondary);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Job Content */
.job-content {
    margin-bottom: 2rem;
}

.job-title-section {
    margin-bottom: 1rem;
}

.job-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
    line-height: 1.3;
}

.job-department {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.95rem;
}

.dept-icon {
    font-size: 1.1rem;
    opacity: 0.8;
}

.job-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 1rem 0;
    font-size: 0.95rem;
}

/* Job Highlights */
.job-highlights {
    display: flex;
    gap: 1.5rem;
    margin: 1.5rem 0;
    flex-wrap: wrap;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.highlight-icon {
    font-size: 1rem;
    opacity: 0.8;
}

/* Job Skills */
.job-skills {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin: 1.5rem 0;
}

.skill-tag {
    background: var(--background-secondary);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid var(--border-light);
    transition: all 0.2s ease;
}

.skill-tag.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    border-color: var(--primary-color);
}

.skill-tag:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Job Footer */
.job-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-light);
    gap: 1rem;
}

.job-stats {
    display: flex;
    gap: 1rem;
    flex: 1;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--text-disabled);
    font-size: 0.85rem;
    font-weight: 500;
}

.stat-icon {
    font-size: 0.9rem;
    opacity: 0.7;
}

.job-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.save-job {
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.save-job:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(139, 92, 246, 0.05);
}

.apply-job {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border: none;
    color: white;
    padding: 0.5rem 1.25rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    box-shadow: 0 2px 10px rgba(139, 92, 246, 0.3);
}

.apply-job:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .featured-jobs-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1.5rem;
    }
    
    .section-title {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .enterprise-opportunities {
        padding: 3rem 0;
    }
    
    .opportunities-container {
        padding: 0 1rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.5rem;
    }
    
    .header-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .quick-filters {
        gap: 0.75rem;
    }
    
    .filter-chip {
        padding: 0.625rem 1rem;
        font-size: 0.9rem;
    }
    
    .featured-jobs-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .enterprise-job-card {
        padding: 1.5rem;
    }
    
    .job-highlights {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .job-footer {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .job-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .save-job,
    .apply-job {
        flex: 1;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .section-title {
        font-size: 1.75rem;
    }
    
    .section-subtitle {
        font-size: 1.1rem;
    }
    
    .quick-filters {
        flex-direction: column;
    }
    
    .filter-chip {
        justify-content: center;
    }
    
    .enterprise-job-card {
        padding: 1.25rem;
    }
    
    .job-title {
        font-size: 1.25rem;
    }
    
    .job-skills {
        gap: 0.375rem;
    }
    
    .skill-tag {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }
}
