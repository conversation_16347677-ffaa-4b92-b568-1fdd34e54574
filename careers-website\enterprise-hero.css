/* Enterprise Hero Section */

.enterprise-hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(30, 64, 175, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
    background-size: 400px 400px;
    animation: patternFloat 20s ease-in-out infinite;
}

@keyframes patternFloat {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(1deg); }
    66% { transform: translate(-20px, 20px) rotate(-1deg); }
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(30, 64, 175, 0.02) 0%,
        rgba(255, 255, 255, 0.8) 50%,
        rgba(59, 130, 246, 0.02) 100%);
}

.hero-container {
    position: relative;
    z-index: 2;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    min-height: 100vh;
}

/* Hero Content */
.hero-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    animation: slideInLeft 1s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 4px 20px rgba(30, 64, 175, 0.3);
    width: fit-content;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.badge-icon {
    font-size: 1.1rem;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    color: var(--text-primary);
    margin: 0;
}

.title-highlight {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.title-highlight::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 2px;
    opacity: 0.3;
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin: 0;
    max-width: 90%;
}

/* Hero Stats */
.hero-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin: 1rem 0;
}

.stat-item {
    text-align: center;
    padding: 1.5rem 1rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Hero Actions */
.hero-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.hero-cta {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(30, 64, 175, 0.3);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.hero-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(30, 64, 175, 0.4);
}

.hero-secondary {
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.hero-secondary:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(30, 64, 175, 0.05);
    transform: translateY(-2px);
}

/* Hero Features */
.hero-features {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.95rem;
}

.feature-icon {
    font-size: 1.1rem;
    opacity: 0.8;
}

/* Hero Visual */
.hero-visual {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: slideInRight 1s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.visual-container {
    position: relative;
    width: 100%;
    max-width: 500px;
    height: 400px;
}

.ai-visualization {
    position: relative;
    width: 100%;
    height: 100%;
}

.ai-node {
    position: absolute;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, white 0%, #f8f9fa 100%);
    border: 2px solid var(--border-light);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    animation: float 6s ease-in-out infinite;
}

.ai-node:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 40px rgba(30, 64, 175, 0.2);
}

.ai-node-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.ai-node-2 {
    top: 10%;
    right: 20%;
    animation-delay: 1.5s;
}

.ai-node-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 3s;
}

.ai-node-4 {
    bottom: 20%;
    right: 10%;
    animation-delay: 4.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.node-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
}

.node-icon {
    font-size: 2rem;
    margin-bottom: 0.25rem;
}

.node-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.connection-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0.6;
}

.connection-lines path {
    stroke-dasharray: 5, 5;
    animation: dash 3s linear infinite;
}

@keyframes dash {
    to {
        stroke-dashoffset: -10;
    }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    animation: fadeInUp 2s ease-out 1s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.scroll-text {
    font-size: 0.9rem;
    font-weight: 500;
}

.scroll-arrow {
    animation: bounce 2s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .visual-container {
        max-width: 400px;
        height: 300px;
    }
}

@media (max-width: 768px) {
    .hero-container {
        padding: 0 1rem;
        gap: 2rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
        max-width: 100%;
    }
    
    .hero-stats {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .stat-item {
        padding: 1rem 0.5rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .hero-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .hero-cta,
    .hero-secondary {
        width: 100%;
        justify-content: center;
    }
    
    .hero-features {
        justify-content: center;
        gap: 1rem;
    }
    
    .visual-container {
        max-width: 300px;
        height: 250px;
    }
    
    .ai-node {
        width: 80px;
        height: 80px;
    }
    
    .node-icon {
        font-size: 1.5rem;
    }
    
    .node-label {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .hero-features {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }
}
